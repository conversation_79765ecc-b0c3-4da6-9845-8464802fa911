import type { RecommendationModel, WorkoutLogSerieModel } from '@/types'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'
import { WarmupCalculator, type WarmupSet } from './warmupCalculator'

interface GenerateAllSetsParams {
  recommendation: RecommendationModel | null
  completedSets: WorkoutLogSerieModel[]
  currentSetIndex: number
  setData: { reps: number; weight: number } | null
  unit: 'kg' | 'lbs'
}

/**
 * Generates all sets (warmup and work sets) based on recommendation and completed sets
 */
export function generateAllSets({
  recommendation,
  completedSets,
  currentSetIndex,
  setData,
  unit,
}: GenerateAllSetsParams): (WorkoutLogSerieModel &
  Partial<WorkoutLogSerieModelRef>)[] {
  if (!recommendation) return []

  const sets: (WorkoutLogSerieModel & Partial<WorkoutLogSerieModelRef>)[] = []
  const warmupCount = recommendation.WarmupsCount || 0
  const workSetCount = recommendation.Series || 0

  // Always calculate warmups locally (following guide)
  let calculatedWarmups: WarmupSet[] = []
  if (warmupCount > 0) {
    try {
      calculatedWarmups = WarmupCalculator.computeWarmups({
        warmupsCount: warmupCount,
        workingWeight: recommendation.Weight,
        workingReps: recommendation.Reps,
        incrementValue: recommendation.Increments?.Kg || 2.5,
        minWeight: recommendation.Min?.Kg,
        maxWeight: recommendation.Max?.Kg,
        isPlateAvailable: false, // Conservative default
        isBodyweight: recommendation.IsBodyweight || false,
        barbellWeight: 20, // Default 20kg barbell
        availablePlates: 'all',
        userBodyWeight: 70, // Default 70kg
        isKg: unit === 'kg',
      })
    } catch (error) {
      calculatedWarmups = []
    }
  }

  // Add warmup sets
  for (let i = 0; i < warmupCount; i++) {
    const isCompleted = completedSets.some(
      (s) => s.IsWarmups && parseInt(s.SetNo || '0') === i + 1
    )
    const completedSet = completedSets.find(
      (s) => s.IsWarmups && parseInt(s.SetNo || '0') === i + 1
    )
    const isActiveSet = !isCompleted && currentSetIndex === i

    // Always use calculated warmups (following guide)
    const calculatedWarmup = calculatedWarmups[i]

    // Use calculated values or safe fallbacks
    const warmupReps = calculatedWarmup?.WarmUpReps || 5
    const warmupWeight = calculatedWarmup?.WarmUpWeightSet || { Lb: 0, Kg: 0 }

    sets.push({
      Id: completedSet?.Id || -(1000 + i), // Use larger negative numbers to avoid conflicts
      SetNo: `${i + 1}`,
      // Use standard Reps and Weight for warmup sets
      Reps:
        isActiveSet && setData
          ? setData.reps
          : completedSet?.Reps || warmupReps,
      Weight: (() => {
        if (isActiveSet && setData) {
          return unit === 'kg'
            ? { Lb: Math.round(setData.weight * 2.20462), Kg: setData.weight }
            : { Lb: setData.weight, Kg: Math.round(setData.weight / 2.20462) }
        }
        return completedSet?.Weight || warmupWeight
      })(),
      IsFinished: isCompleted,
      IsNext: isActiveSet,
      IsWarmups: true,
    })
  }

  // Calculate total work sets including rest-pause mini-sets
  const isRestPause =
    !recommendation.IsNormalSets &&
    !recommendation.IsPyramid &&
    !recommendation.IsReversePyramid &&
    recommendation.NbPauses > 0

  const totalWorkSets = isRestPause
    ? workSetCount + (recommendation.NbPauses || 0)
    : workSetCount

  // Debug logging for rest-pause
  if (isRestPause || recommendation.NbPauses > 0) {
    // Rest-pause detected
  }

  // Add work sets
  for (let i = 0; i < totalWorkSets; i++) {
    const setIndex = warmupCount + i
    const isCompleted = completedSets.some(
      (s) => !s.IsWarmups && parseInt(s.SetNo || '0') === setIndex + 1
    )
    const completedSet = completedSets.find(
      (s) => !s.IsWarmups && parseInt(s.SetNo || '0') === setIndex + 1
    )
    const isActiveSet = !isCompleted && currentSetIndex === setIndex

    // Determine SetTitle based on recommendation type and set position
    let setTitle = ''
    let isBackOffSet = false
    let isDropSet = false
    let nbPause = 0

    // Rest-pause sets: first Series sets are normal, additional NbPauses sets are rest-pause
    if (isRestPause) {
      if (i < workSetCount) {
        // Main working set(s)
        setTitle = i === 0 ? '1st work set' : `Set ${i + 1}`
        nbPause = 0
      } else {
        // Rest-pause mini-sets
        const restPauseIndex = i - workSetCount + 1
        setTitle =
          restPauseIndex === 1
            ? "All right! Now let's try:"
            : `Rest-pause set ${restPauseIndex}`
        nbPause = 1 // Mark as rest-pause set
      }
    }
    // Pyramid sets
    else if (recommendation.IsPyramid) {
      if (i === 0 && workSetCount > 1) {
        setTitle = 'Pyramid set:'
      }
    }
    // Drop sets
    else if (recommendation.IsDropSet) {
      setTitle = 'Drop set'
      isDropSet = true
    }
    // Back-off sets
    else if (recommendation.IsBackOffSet) {
      setTitle = 'Back-off set:'
      isBackOffSet = true
    }
    // Normal working sets
    else if (i === 0 && workSetCount > 1) {
      setTitle = 'Working sets:'
    }

    // Determine reps based on set type
    const targetReps = (() => {
      // Rest-pause sets: main working sets use regular Reps, rest-pause mini-sets use NbRepsPauses
      if (isRestPause && i >= workSetCount) {
        // This is a rest-pause mini-set
        return recommendation.NbRepsPauses || recommendation.Reps || 10
      }
      return recommendation.Reps || 10
    })()

    sets.push({
      Id: completedSet?.Id || -(2000 + i), // Use different range for work sets
      SetNo: `${setIndex + 1}`,
      Reps:
        isActiveSet && setData
          ? setData.reps
          : completedSet?.Reps || targetReps,
      Weight: (() => {
        if (isActiveSet && setData) {
          return unit === 'kg'
            ? { Lb: Math.round(setData.weight * 2.20462), Kg: setData.weight }
            : { Lb: setData.weight, Kg: Math.round(setData.weight / 2.20462) }
        }
        return completedSet?.Weight || recommendation.Weight || { Lb: 0, Kg: 0 }
      })(),
      IsFinished: isCompleted,
      IsNext: isActiveSet,
      IsWarmups: false,
      SetTitle: setTitle,
      IsBackOffSet: isBackOffSet,
      IsDropSet: isDropSet,
      NbPause: nbPause,
      restTime:
        isRestPause && i >= workSetCount ? recommendation.RpRest : undefined,
    })
  }

  // If no set is marked as next, mark the first incomplete set as next
  if (!sets.some((s) => s.IsNext)) {
    const firstIncompleteIndex = sets.findIndex((s) => !s.IsFinished)
    if (firstIncompleteIndex !== -1 && sets[firstIncompleteIndex]) {
      sets[firstIncompleteIndex].IsNext = true
    }
  }

  return sets
}

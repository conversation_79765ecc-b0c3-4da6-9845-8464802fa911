'use client'

import { formatWeight } from '@/utils/weightUtils'
import type { WarmupSet } from '@/utils/warmupCalculator'

interface WarmupSetDisplayProps {
  warmupSets: WarmupSet[]
  isBodyweight: boolean
}

export function WarmupSetDisplay({
  warmupSets,
  isBodyweight,
}: WarmupSetDisplayProps) {
  if (warmupSets.length === 0) {
    return null
  }

  return (
    <div
      className="px-4 py-3 bg-bg-secondary border-b border-brand-primary/10"
      data-testid="warmup-sets-container"
    >
      <h3 className="text-lg font-semibold text-text-primary mb-3">
        Warm-up Sets
      </h3>
      <div className="space-y-2">
        {warmupSets.map((warmup, index) => {
          const isBodyweightWarmup =
            warmup.WarmUpWeightSet.Lb === 0 && !isBodyweight

          return (
            <div
              // eslint-disable-next-line react/no-array-index-key
              key={`warmup-${index}`}
              data-testid={`warmup-set-${index}`}
              className="flex items-center justify-between p-3 bg-bg-primary rounded-lg shadow-theme-sm"
            >
              <div className="flex items-center gap-3">
                <span className="text-xs px-2 py-1 rounded-full bg-warning/20 text-warning font-medium">
                  Warm-up
                </span>
                <div>
                  <p className="text-text-primary font-medium">
                    Set {index + 1}
                  </p>
                  <p className="text-sm text-text-secondary">
                    {warmup.WarmUpReps} reps
                    {!isBodyweight && (
                      <>
                        {' @ '}
                        {isBodyweightWarmup
                          ? 'Bodyweight'
                          : formatWeight(warmup.WarmUpWeightSet, 'lbs')}
                      </>
                    )}
                  </p>
                </div>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

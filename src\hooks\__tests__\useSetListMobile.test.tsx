import { renderHook } from '@testing-library/react'
import { useSetListMobile } from '../useSetListMobile'
import type { ExerciseModel, RecommendationModel } from '@/types/api'

describe('useSetListMobile', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    IsBodyweight: false,
    IsTimeBased: false,
    IsFlexibility: false,
    IsUnilateral: false,
    IsSystemExercise: true,
    BodyPartId: 1,
  }

  const mockRecommendation: RecommendationModel = {
    ExerciseId: 1,
    ExerciseName: 'Bench Press',
    Series: 3,
    Reps: 10,
    Weight: { Kg: 60, Lb: 132 },
    RIR: 2,
    Increments: { Kg: 2.5, Lb: 5 },
    Min: { Kg: 0, Lb: 0 },
    Max: { Kg: 200, Lb: 440 },
    WarmUpsList: [],
  }

  describe('massUnit parameter', () => {
    it('should use kg when massUnit is kg', () => {
      const { result } = renderHook(() =>
        useSetListMobile(mockExercise, mockRecommendation, 'kg')
      )

      expect(result.current.massUnit).toBe('kg')

      const firstSet = result.current.exerciseWorkSets?.Sets[0]
      expect(firstSet?.WeightSingal).toBe('60.00')
      expect(firstSet?.WeightDouble).toBe('60.00')
    })

    it('should use lb when massUnit is lb', () => {
      const { result } = renderHook(() =>
        useSetListMobile(mockExercise, mockRecommendation, 'lb')
      )

      expect(result.current.massUnit).toBe('lb')

      const firstSet = result.current.exerciseWorkSets?.Sets[0]
      expect(firstSet?.WeightSingal).toBe('132.00')
      expect(firstSet?.WeightDouble).toBe('132.00')
    })

    it('should default to kg when massUnit is not provided', () => {
      const { result } = renderHook(() =>
        useSetListMobile(mockExercise, mockRecommendation)
      )

      expect(result.current.massUnit).toBe('kg')
    })
  })

  describe('null safety for Weight', () => {
    it('should handle null Weight gracefully', () => {
      const recommendationWithNullWeight: RecommendationModel = {
        ...mockRecommendation,
        Weight: null as any,
      }

      const { result } = renderHook(() =>
        useSetListMobile(mockExercise, recommendationWithNullWeight, 'kg')
      )

      const firstSet = result.current.exerciseWorkSets?.Sets[0]
      expect(firstSet?.WeightSingal).toBe('0.00')
      expect(firstSet?.WeightDouble).toBe('0.00')
    })

    it('should handle undefined Weight gracefully', () => {
      const recommendationWithUndefinedWeight: RecommendationModel = {
        ...mockRecommendation,
        Weight: undefined as any,
      }

      const { result } = renderHook(() =>
        useSetListMobile(mockExercise, recommendationWithUndefinedWeight, 'kg')
      )

      const firstSet = result.current.exerciseWorkSets?.Sets[0]
      expect(firstSet?.WeightSingal).toBe('0.00')
      expect(firstSet?.WeightDouble).toBe('0.00')
    })

    it('should handle warmup sets with null Weight', () => {
      const recommendationWithWarmups: RecommendationModel = {
        ...mockRecommendation,
        WarmupsCount: 1, // Must specify count when providing WarmUpsList
        WarmUpsList: [
          {
            WarmUpReps: 10,
            WarmUpWeightSet: null as any,
          },
        ],
      }

      const { result } = renderHook(() =>
        useSetListMobile(mockExercise, recommendationWithWarmups, 'kg')
      )

      const warmupSet = result.current.exerciseWorkSets?.Sets[0]
      expect(warmupSet?.IsWarmups).toBe(true)
      expect(warmupSet?.WeightSingal).toBe('0.00')
    })

    it('should use provided WarmUpsList values when available', () => {
      // Test rationale: This tests the actual bug - when WarmUpsList is provided
      // from API, warmup sets should use those values, not work set values
      const recommendationWithProvidedWarmups: RecommendationModel = {
        ...mockRecommendation,
        Weight: { Kg: 100, Lb: 220 },
        Reps: 10,
        Series: 3,
        WarmupsCount: 2,
        WarmUpsList: [
          {
            WarmUpReps: 8,
            WarmUpWeightSet: { Kg: 50, Lb: 110 },
          },
          {
            WarmUpReps: 5,
            WarmUpWeightSet: { Kg: 75, Lb: 165 },
          },
        ],
      }

      const { result } = renderHook(() =>
        useSetListMobile(mockExercise, recommendationWithProvidedWarmups, 'kg')
      )

      const sets = result.current.exerciseWorkSets?.Sets || []

      // Check warmup sets use provided values
      const warmup1 = sets[0]
      const warmup2 = sets[1]

      expect(warmup1?.IsWarmups).toBe(true)
      expect(warmup1?.Reps).toBe(8) // Should use provided value, not work reps (10)
      expect(warmup1?.Weight?.Kg).toBe(50) // Should use provided value, not work weight (100)

      expect(warmup2?.IsWarmups).toBe(true)
      expect(warmup2?.Reps).toBe(5)
      expect(warmup2?.Weight?.Kg).toBe(75)
    })
  })

  describe('SetListMobile component weight display', () => {
    it('should safely display weight when Weight is null', () => {
      const setWithNullWeight = {
        Id: 1,
        Weight: null as any,
        Reps: 10,
        IsBodyweight: false,
      }

      // This test is for the component logic that we'll fix
      // The formatWeight function should handle null Weight
      expect(() => {
        const weight = setWithNullWeight.Weight?.Kg || 0
        const formatted = `${weight} kg`
        return formatted
      }).not.toThrow()
    })
  })

  describe('warmup sets display values', () => {
    it('should display correct warmup values, not work set values', () => {
      // Test rationale: This test verifies the bug where warmup sets show
      // work set values instead of calculated warmup values before saving
      const recommendationWithWarmups: RecommendationModel = {
        ...mockRecommendation,
        Weight: { Kg: 100, Lb: 220 },
        Reps: 10,
        Series: 3,
        WarmupsCount: 3,
        WarmUpsList: [], // Hook should calculate warmups
      }

      const { result } = renderHook(() =>
        useSetListMobile(mockExercise, recommendationWithWarmups, 'kg')
      )

      const sets = result.current.exerciseWorkSets?.Sets || []

      // Should have 3 warmups + 3 work sets
      expect(sets.length).toBe(6)

      // Check warmup sets (first 3 sets)
      const warmup1 = sets[0]
      const warmup2 = sets[1]
      const warmup3 = sets[2]

      // Warmup sets should be marked as warmups
      expect(warmup1?.IsWarmups).toBe(true)
      expect(warmup2?.IsWarmups).toBe(true)
      expect(warmup3?.IsWarmups).toBe(true)

      // Warmup weights should be progressively increasing, NOT equal to work weight
      // Based on WarmupCalculator logic: 50%, 67.5%, 85% of work weight
      expect(warmup1?.Weight?.Kg).toBe(50) // 50% of 100kg
      expect(warmup2?.Weight?.Kg).toBe(67.5) // 67.5% of 100kg
      expect(warmup3?.Weight?.Kg).toBe(85) // 85% of 100kg

      // Warmup reps should decrease, NOT equal to work reps (10)
      // Based on WarmupCalculator: starts at 75% of work reps, decreases to 40%
      expect(warmup1?.Reps).toBe(8) // 75% of 10 = 7.5, rounds to 8
      expect(warmup2?.Reps).toBe(6) // Progressive decrease
      expect(warmup3?.Reps).toBe(4) // 40% of 10 = 4

      // Work sets should have correct values
      const workSet1 = sets[3]
      expect(workSet1?.IsWarmups).toBe(false)
      expect(workSet1?.Weight?.Kg).toBe(100)
      expect(workSet1?.Reps).toBe(10)
    })

    it('should calculate warmups when WarmUpsList is empty but WarmupsCount > 0', () => {
      // Test rationale: Verify hook calculates warmups when not provided
      const recommendationWithWarmupsCount: RecommendationModel = {
        ...mockRecommendation,
        Weight: { Kg: 80, Lb: 176 },
        Reps: 8,
        WarmupsCount: 2,
        WarmUpsList: [],
      }

      const { result } = renderHook(() =>
        useSetListMobile(mockExercise, recommendationWithWarmupsCount, 'kg')
      )

      const sets = result.current.exerciseWorkSets?.Sets || []
      const warmupSets = sets.filter((s) => s.IsWarmups)

      expect(warmupSets.length).toBe(2)

      // Verify calculated values are not equal to work values
      warmupSets.forEach((warmup) => {
        expect(warmup.Weight?.Kg).not.toBe(80) // Should not equal work weight
      })
    })
  })
})

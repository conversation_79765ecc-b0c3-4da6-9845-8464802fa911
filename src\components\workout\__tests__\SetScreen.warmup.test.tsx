import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { SetScreen } from '../SetScreen'
import { NavigationProvider } from '@/contexts/NavigationContext'
import type { RecommendationModel } from '@/types'

const mockRecommendation: RecommendationModel = {
  Series: 3,
  Reps: 10,
  Weight: { Lb: 135, Kg: 61.23 },
  WarmupsCount: 2,
  WarmUpsList: [],
  OneRMProgress: 0,
  RecommendationInKg: 61.23,
  OneRMPercentage: 75,
  WarmUpReps1: 0,
  WarmUpReps2: 0,
  WarmUpWeightSet1: { Lb: 0, Kg: 0 },
  WarmUpWeightSet2: { Lb: 0, Kg: 0 },
  RpRest: 180,
  NbPauses: 0,
  NbRepsPauses: 0,
  IsEasy: false,
  IsMedium: true,
  IsBodyweight: false,
  Increments: { Lb: 5, Kg: 2.5 },
  Min: { Lb: 0, Kg: 0 },
  Max: { Lb: 300, Kg: 136.08 },
  IsNormalSets: true,
  IsDeload: false,
  IsBackOffSet: false,
  BackOffSetWeight: { Lb: 0, Kg: 0 },
  IsMaxChallenge: false,
  IsLightSession: false,
  FirstWorkSetReps: 10,
  FirstWorkSetWeight: { Lb: 135, Kg: 61.23 },
  FirstWorkSet1RM: { Lb: 180, Kg: 81.65 },
  IsPyramid: false,
  IsReversePyramid: false,
  HistorySet: [],
  ReferenceSetHistory: {
    Id: 1,
    Reps: 10,
    Weight: { Lb: 125, Kg: 56.7 },
    IsWarmups: false,
    IsNext: false,
    IsFinished: true,
  },
  MinReps: 8,
  MaxReps: 12,
  isPlateAvailable: false,
  isDumbbellAvailable: true,
  isPulleyAvailable: false,
  isBandsAvailable: false,
  Speed: 1,
  IsManual: false,
  ReferenseReps: 10,
  ReferenseWeight: { Lb: 125, Kg: 56.7 },
  IsDropSet: false,
}

// Mock dependencies
const mockSetScreenLogic = {
  currentExercise: {
    Id: 1,
    Label: 'Bench Press',
    IsBodyweight: false,
    IsTimeBased: false,
    IsSystemExercise: true,
    IsSwapTarget: false,
    IsFinished: false,
    IsUnilateral: false,
    IsEasy: false,
    IsMedium: true,
    VideoUrl: '',
    IsNextExercise: false,
    IsPlate: false,
    IsWeighted: true,
    IsPyramid: false,
    IsNormalSets: true,
    IsBodypartPriority: false,
    IsFlexibility: false,
    IsOneHanded: false,
    LocalVideo: '',
    IsAssisted: false,
  },
  exercises: [],
  currentExerciseIndex: 0,
  isWarmup: false,
  totalSets: 5, // 2 warmups + 3 work sets
  currentSetIndex: 0,
  setData: { reps: 10, weight: 135, duration: 45 },
  isSaving: false,
  saveError: null,
  showRIRPicker: false,
  showComplete: false,
  showExerciseComplete: false,
  isTransitioning: false,
  showSetSaved: false,
  recommendation: mockRecommendation,
  isLoading: false,
  error: null,
  isLastExercise: false,
  completedSets: [],
  setSetData: vi.fn(),
  handleSaveSet: vi.fn(),
  handleRIRSelect: vi.fn(),
  handleRIRCancel: vi.fn(),
  refetchRecommendation: vi.fn(),
  performancePercentage: () => 8,
  handleSetClick: vi.fn(),
}

vi.mock('@/hooks/useSetScreenLogic', () => ({
  useSetScreenLogic: () => mockSetScreenLogic,
}))

vi.mock('@/hooks/useOneRMTracking', () => ({
  useOneRMTracking: () => ({
    progressMessage: '+8%',
    lastTimeInfo: 'Last time: 10 x 61.23 kg',
    handleWeightChange: vi.fn(),
    handleRepsChange: vi.fn(),
  }),
}))

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
  usePathname: () => '/workout/exercise/1',
}))

vi.mock('@/utils/warmupCalculator', () => ({
  WarmupCalculator: {
    computeWarmups: vi.fn(() => [
      {
        warmUpWeightSet: { Lb: 50, Kg: 22.5 }, // 50% of working weight
        warmUpReps: 8,
        WarmUpReps: 8,
        WarmUpWeightSet: { Lb: 50, Kg: 22.5 },
      },
      {
        warmUpWeightSet: { Lb: 85, Kg: 38.5 }, // 85% of working weight
        warmUpReps: 6,
        WarmUpReps: 6,
        WarmUpWeightSet: { Lb: 85, Kg: 38.5 },
      },
    ]),
  },
  WEIGHTED_EXERCISE_IDS: [18627, 18628, 21234, 14297],
}))

describe('SetScreen - Warmup Display', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should display warmup sets when recommendation has warmups', () => {
    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    expect(screen.getByText('Warm-up Sets')).toBeInTheDocument()
    expect(screen.getByTestId('warmup-sets-container')).toBeInTheDocument()
  })

  it('should show correct number of warmup sets', () => {
    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    const warmupSets = screen.getAllByTestId(/warmup-set-\d+/)
    expect(warmupSets).toHaveLength(2)
  })

  it('should position warmup sets before exercise info', () => {
    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    const warmupContainer = screen.getByTestId('warmup-sets-container')
    const exerciseInfo = screen.getByTestId('recommendations')

    // Get their parent elements and check order
    const parent = warmupContainer.parentElement
    const children = Array.from(parent?.children || [])
    const warmupIndex = children.indexOf(warmupContainer)
    const exerciseIndex = children.findIndex((el) => el.contains(exerciseInfo))

    expect(warmupIndex).toBeLessThan(exerciseIndex)
  })
})

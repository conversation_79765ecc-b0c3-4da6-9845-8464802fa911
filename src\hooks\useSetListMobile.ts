import { useMemo } from 'react'
import type {
  WorkoutLogSerieModelRef,
  ExerciseWorkSetsModel,
  MassUnit,
} from '@/types/api/WorkoutLogSerieModelRef'
import type { RecommendationModel, ExerciseModel } from '@/types/api'
import { createWarmupSet, createWorkSet } from './useSetListMobileHelpers'
import {
  WarmupCalculator,
  WEIGHTED_EXERCISE_IDS,
} from '@/utils/warmupCalculator'
import { ExerciseHelpers } from '@/utils/exerciseHelpers'

export function useSetListMobile(
  exercise: ExerciseModel | null,
  recommendation: RecommendationModel | null,
  massUnit: MassUnit = 'kg',
  userBodyWeight: number = 70
) {
  const exerciseWorkSets = useMemo<ExerciseWorkSetsModel | null>(() => {
    if (!exercise || !recommendation) return null

    const sets: WorkoutLogSerieModelRef[] = []

    // Use API-provided warmups if available, otherwise calculate them
    const warmupCount = recommendation.WarmupsCount || 0
    if (warmupCount > 0) {
      // Check if we have pre-calculated warmups from the API
      if (recommendation.WarmUpsList && recommendation.WarmUpsList.length > 0) {
        // Use the API-provided warmups
        recommendation.WarmUpsList.forEach((warmup, i) => {
          sets.push(
            createWarmupSet(
              warmup,
              i,
              exercise,
              recommendation,
              massUnit,
              recommendation.WarmUpsList!.length
            )
          )
        })
      } else {
        // Calculate warmups locally (existing logic)
        const exerciseInfo = ExerciseHelpers.getExerciseInfo(exercise.Label)
        const userUnit = massUnit === 'kg' ? 'kg' : 'lbs'
        const config = {
          warmupsCount: warmupCount,
          workingWeight: recommendation.Weight || { Kg: 0, Lb: 0 },
          workingReps: recommendation.Reps || 0,
          incrementValue: massUnit === 'kg' ? 2.5 : 5, // Standard increments
          isPlateAvailable: exerciseInfo.usesPlates,
          isDumbbellAvailable: false, // TODO: Get from user settings
          isBandsAvailable: false, // TODO: Get from user settings
          isPulleyAvailable: false, // TODO: Get from user settings
          isBodyweight: exerciseInfo.isBodyweight,
          isWeighted: WEIGHTED_EXERCISE_IDS.includes(exercise.Id),
          isAssisted: false, // TODO: Detect assisted exercises
          barbellWeight: ExerciseHelpers.getBarbellWeight(exercise.Label, {
            unit: userUnit,
            bodyWeight: userBodyWeight,
          }),
          availablePlates: ExerciseHelpers.getAvailablePlates({
            unit: userUnit,
          }),
          availableDumbbells: 'all', // TODO: Get from user settings
          availableBands: 'all', // TODO: Get from user settings
          userBodyWeight,
          isKg: massUnit === 'kg',
        }

        const calculatedWarmups = WarmupCalculator.computeWarmups(config)

        // Convert calculated warmups to WorkoutLogSerieModelRef format
        calculatedWarmups.forEach((warmup, i) => {
          sets.push(
            createWarmupSet(
              {
                WarmUpReps: warmup.WarmUpReps,
                WarmUpWeightSet: warmup.WarmUpWeightSet,
              },
              i,
              exercise,
              recommendation,
              massUnit,
              calculatedWarmups.length
            )
          )
        })
      }
    }

    // Add work sets
    const numWorkSets = recommendation.Series ?? 0 // Don't generate sets if Series is undefined or null
    for (let i = 0; i < numWorkSets; i++) {
      sets.push(
        createWorkSet(i, exercise, recommendation, massUnit, numWorkSets)
      )
    }

    return {
      Id: exercise.Id,
      Label: exercise.Label,
      CountNo: '1',
      IsBodyweight: exercise.IsBodyweight || false,
      IsSystemExercise: exercise.IsSystemExercise || false,
      IsFlexibility: exercise.IsFlexibility || false,
      IsTimeBased: exercise.IsTimeBased || false,
      IsUnilateral: exercise.IsUnilateral || false,
      IsFinished: false,
      IsNextExercise: true,
      IsSelected: false,
      IsRecoLoaded: true,
      VideoUrl: exercise.VideoUrl || '',
      HeaderImage: '',
      BodyPartId: exercise.BodyPartId,
      Sets: sets,
      Clear() {
        this.Sets = []
      },
      get Count() {
        return this.Sets.length
      },
      Add(set: WorkoutLogSerieModelRef) {
        this.Sets.push(set)
      },
    }
  }, [exercise, recommendation, massUnit, userBodyWeight])

  const updateSetState = (
    setIndex: number,
    updates: Partial<WorkoutLogSerieModelRef>
  ) => {
    if (!exerciseWorkSets) return

    const set = exerciseWorkSets.Sets[setIndex]
    if (!set) return

    Object.assign(set, updates)
  }

  const markSetComplete = (setIndex: number) => {
    if (!exerciseWorkSets) return

    updateSetState(setIndex, { IsFinished: true, IsNext: false })

    // Move IsNext to next unfinished set
    const nextIndex = exerciseWorkSets.Sets.findIndex(
      (s, i) => i > setIndex && !s.IsFinished && !s.IsWarmups
    )
    if (nextIndex !== -1) {
      updateSetState(nextIndex, { IsNext: true })
    }
  }

  return {
    exerciseWorkSets,
    massUnit,
    updateSetState,
    markSetComplete,
  }
}

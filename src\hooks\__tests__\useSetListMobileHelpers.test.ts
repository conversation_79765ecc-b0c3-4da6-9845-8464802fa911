import { describe, it, expect } from 'vitest'
import type { RecommendationModel, ExerciseModel } from '@/types/api/exercise'
import type { MassUnit } from '@/types/api/WorkoutLogSerieModelRef'
import { createWarmupSet, createWorkSet } from '../useSetListMobileHelpers'

describe('useSetListMobileHelpers', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    IsFlexibility: false,
    IsTimeBased: false,
    IsUnilateral: false,
    IsBodyweight: false,
    VideoUrl: 'https://example.com/video.mp4'
  }

  const mockRecommendation: RecommendationModel = {
    Reps: 10,
    Weight: { Kg: 100, Lb: 220 },
    RIR: 2,
    Increments: { Kg: 2.5, Lb: 5 },
    Min: { Kg: 0, Lb: 0 },
    Max: { Kg: 200, Lb: 440 }
  }

  describe('createWarmupSet', () => {
    it('should create warmup set with correct basic properties', () => {
      const warmup = {
        WarmUpReps: 8,
        WarmUpWeightSet: { Kg: 50, Lb: 110 }
      }

      const result = createWarmupSet(warmup, 0, mockExercise, mockRecommendation, 'kg', 3)

      expect(result.Id).toBe(-1000)
      expect(result.ExerciseId).toBe(1)
      expect(result.Reps).toBe(8)
      expect(result.Weight).toEqual({ Kg: 50, Lb: 110 })
      expect(result.IsWarmups).toBe(true)
      expect(result.ExerciseName).toBe('Bench Press')
    })

    it('should set warmup-specific properties correctly', () => {
      const warmup = {
        WarmUpReps: 6,
        WarmUpWeightSet: { Kg: 75, Lb: 165 }
      }

      const result = createWarmupSet(warmup, 1, mockExercise, mockRecommendation, 'kg', 3)

      expect(result.SetNo).toBe('2')
      expect(result.SetTitle).toBe('Warmup 2')
      expect(result.IsLastWarmupSet).toBe(false)
      expect(result.IsFirstWorkSet).toBe(false)
      expect(result.IsNext).toBe(false)
      expect(result.IsFinished).toBe(false)
      expect(result.IsActive).toBe(false)
    })

    it('should mark last warmup set correctly', () => {
      const warmup = {
        WarmUpReps: 4,
        WarmUpWeightSet: { Kg: 85, Lb: 187 }
      }

      const result = createWarmupSet(warmup, 2, mockExercise, mockRecommendation, 'kg', 3)

      expect(result.IsLastWarmupSet).toBe(true)
      expect(result.SetNo).toBe('3')
      expect(result.SetTitle).toBe('Warmup 3')
    })

    it('should handle exercise-specific properties', () => {
      const bodyweightExercise: ExerciseModel = {
        ...mockExercise,
        Label: 'Push-ups',
        IsBodyweight: true,
        IsFlexibility: true,
        IsTimeBased: true,
        IsUnilateral: true
      }

      const warmup = {
        WarmUpReps: 10,
        WarmUpWeightSet: { Kg: 80, Lb: 176 }
      }

      const result = createWarmupSet(warmup, 0, bodyweightExercise, mockRecommendation, 'kg', 2)

      expect(result.IsBodyweight).toBe(true)
      expect(result.IsFlexibility).toBe(true)
      expect(result.IsTimeBased).toBe(true)
      expect(result.IsUnilateral).toBe(true)
      expect(result.ExerciseName).toBe('Push-ups')
    })

    it('should calculate weight display correctly in kg', () => {
      const warmup = {
        WarmUpReps: 8,
        WarmUpWeightSet: { Kg: 50.75, Lb: 111.83 }
      }

      const result = createWarmupSet(warmup, 0, mockExercise, mockRecommendation, 'kg', 2)

      expect(result.WeightSingal).toBe('50.75')
      expect(result.WeightDouble).toBe('50.75')
    })

    it('should calculate weight display correctly in lb', () => {
      const warmup = {
        WarmUpReps: 8,
        WarmUpWeightSet: { Kg: 50, Lb: 110.25 }
      }

      const result = createWarmupSet(warmup, 0, mockExercise, mockRecommendation, 'lb', 2)

      expect(result.WeightSingal).toBe('110.25')
      expect(result.WeightDouble).toBe('110.25')
    })

    it('should handle null weight values', () => {
      const warmup = {
        WarmUpReps: 8,
        WarmUpWeightSet: null as any
      }

      const result = createWarmupSet(warmup, 0, mockExercise, mockRecommendation, 'kg', 2)

      expect(result.WeightSingal).toBe('0.00')
      expect(result.WeightDouble).toBe('0.00')
    })

    it('should set default values for missing recommendation properties', () => {
      const minimalRecommendation = {
        Reps: 10,
        Weight: { Kg: 100, Lb: 220 }
      } as RecommendationModel

      const warmup = {
        WarmUpReps: 8,
        WarmUpWeightSet: { Kg: 50, Lb: 110 }
      }

      const result = createWarmupSet(warmup, 0, mockExercise, minimalRecommendation, 'kg', 2)

      expect(result.Increments).toEqual({ Kg: 2.5, Lb: 5 })
      expect(result.Min).toEqual({ Kg: 0, Lb: 0 })
      expect(result.Max).toEqual({ Kg: 200, Lb: 440 })
    })

    it('should set transparent background color for warmup sets', () => {
      const warmup = {
        WarmUpReps: 8,
        WarmUpWeightSet: { Kg: 50, Lb: 110 }
      }

      const result = createWarmupSet(warmup, 0, mockExercise, mockRecommendation, 'kg', 2)

      expect(result.BackColor).toBe('transparent')
    })

    it('should have correct default boolean flags', () => {
      const warmup = {
        WarmUpReps: 8,
        WarmUpWeightSet: { Kg: 50, Lb: 110 }
      }

      const result = createWarmupSet(warmup, 0, mockExercise, mockRecommendation, 'kg', 2)

      expect(result.RIR).toBe(0)
      expect(result.IsAssisted).toBe(false)
      expect(result.IsLastSet).toBe(false)
      expect(result.IsFirstSide).toBe(true)
      expect(result.IsHeaderCell).toBe(false)
      expect(result.IsFirstSetFinished).toBe(false)
      expect(result.IsBackOffSet).toBe(false)
      expect(result.IsDropSet).toBe(false)
      expect(result.IsNormalset).toBe(false)
      expect(result.IsMaxChallenge).toBe(false)
      expect(result.IsTimerOff).toBe(false)
      expect(result.ShowWorkTimer).toBe(false)
    })
  })

  describe('createWorkSet', () => {
    it('should create work set with correct basic properties', () => {
      const result = createWorkSet(0, mockExercise, mockRecommendation, 'kg', 3)

      expect(result.Id).toBe(1)
      expect(result.ExerciseId).toBe(1)
      expect(result.Reps).toBe(10)
      expect(result.Weight).toEqual({ Kg: 100, Lb: 220 })
      expect(result.IsWarmups).toBe(false)
      expect(result.RIR).toBe(2)
      expect(result.ExerciseName).toBe('Bench Press')
    })

    it('should mark first work set correctly', () => {
      const result = createWorkSet(0, mockExercise, mockRecommendation, 'kg', 3)

      expect(result.IsNext).toBe(true)
      expect(result.IsFirstWorkSet).toBe(true)
      expect(result.IsLastSet).toBe(false)
      expect(result.SetNo).toBe('1')
      expect(result.SetTitle).toBe('Set 1')
    })

    it('should mark last work set correctly', () => {
      const result = createWorkSet(2, mockExercise, mockRecommendation, 'kg', 3)

      expect(result.IsNext).toBe(false)
      expect(result.IsFirstWorkSet).toBe(false)
      expect(result.IsLastSet).toBe(true)
      expect(result.SetNo).toBe('3')
      expect(result.SetTitle).toBe('Set 3')
    })

    it('should handle middle work sets correctly', () => {
      const result = createWorkSet(1, mockExercise, mockRecommendation, 'kg', 3)

      expect(result.IsNext).toBe(false)
      expect(result.IsFirstWorkSet).toBe(false)
      expect(result.IsLastSet).toBe(false)
      expect(result.SetNo).toBe('2')
      expect(result.SetTitle).toBe('Set 2')
    })

    it('should handle recommendation set type flags', () => {
      const specialRecommendation: RecommendationModel = {
        ...mockRecommendation,
        IsBackOffSet: true,
        IsDropSet: true,
        IsNormalSets: true,
        IsMaxChallenge: true
      }

      const result = createWorkSet(0, mockExercise, specialRecommendation, 'kg', 3)

      expect(result.IsBackOffSet).toBe(true)
      expect(result.IsDropSet).toBe(true)
      expect(result.IsNormalset).toBe(true)
      expect(result.IsMaxChallenge).toBe(true)
    })

    it('should calculate background color for finished/next sets', () => {
      const result = createWorkSet(0, mockExercise, mockRecommendation, 'kg', 3)

      // First set is marked as IsNext = true
      expect(result.BackColor).toBe('#4D0C2432')

      // Test with finished set
      const finishedResult = createWorkSet(1, mockExercise, mockRecommendation, 'kg', 3)
      finishedResult.IsFinished = true
      expect(finishedResult.BackColor).toBe('#4D0C2432')

      // Test with normal set
      const normalResult = createWorkSet(1, mockExercise, mockRecommendation, 'kg', 3)
      expect(normalResult.BackColor).toBe('transparent')
    })

    it('should handle history set data', () => {
      const recommendationWithHistory: RecommendationModel = {
        ...mockRecommendation,
        LastLogDate: '2024-01-15T10:30:00Z',
        HistorySet: [
          {
            Reps: 8,
            Weight: { Kg: 90, Lb: 198 }
          }
        ]
      }

      const result = createWorkSet(0, mockExercise, recommendationWithHistory, 'kg', 3)

      expect(result.LastTimeSet).toBe(new Date('2024-01-15T10:30:00Z').toLocaleDateString())
      expect(result.PreviousReps).toBe(8)
      expect(result.PreviousWeight).toEqual({ Kg: 90, Lb: 198 })
    })

    it('should handle missing history data', () => {
      const result = createWorkSet(0, mockExercise, mockRecommendation, 'kg', 3)

      expect(result.LastTimeSet).toBe('')
      expect(result.PreviousReps).toBe(0)
      expect(result.PreviousWeight).toEqual({ Kg: 0, Lb: 0 })
    })

    it('should set work timer to true for work sets', () => {
      const result = createWorkSet(0, mockExercise, mockRecommendation, 'kg', 3)

      expect(result.ShowWorkTimer).toBe(true)
    })

    it('should handle speed from recommendation', () => {
      const recommendationWithSpeed: RecommendationModel = {
        ...mockRecommendation,
        Speed: 2.5
      }

      const result = createWorkSet(0, mockExercise, recommendationWithSpeed, 'kg', 3)

      expect(result.Speed).toBe(2.5)
    })

    it('should default speed to 1.0 when not provided', () => {
      const result = createWorkSet(0, mockExercise, mockRecommendation, 'kg', 3)

      expect(result.Speed).toBe(1.0)
    })

    it('should handle default RIR when not provided', () => {
      const recommendationNoRIR = {
        ...mockRecommendation,
        RIR: undefined
      }

      const result = createWorkSet(0, mockExercise, recommendationNoRIR, 'kg', 3)

      expect(result.RIR).toBe(2)
    })

    it('should never be last warmup set for work sets', () => {
      const result = createWorkSet(0, mockExercise, mockRecommendation, 'kg', 3)

      expect(result.IsLastWarmupSet).toBe(false)
    })
  })
})
import { describe, it, expect, beforeEach } from 'vitest'
import { useWorkoutStore } from '../workoutStore'
import { act, renderHook } from '@testing-library/react'

describe('workoutStore', () => {
  beforeEach(() => {
    localStorage.clear()
    const { result } = renderHook(() => useWorkoutStore())
    act(() => {
      result.current.resetWorkout()
    })
  })

  describe('getRestDuration', () => {
    it('should return default duration when no preference is set', () => {
      const { result } = renderHook(() => useWorkoutStore())

      expect(result.current.getRestDuration()).toBe(120)
    })

    it('should return stored duration from localStorage', () => {
      localStorage.setItem('restDuration', '180')

      const { result } = renderHook(() => useWorkoutStore())

      expect(result.current.getRestDuration()).toBe(180)
    })

    it('should handle invalid localStorage values', () => {
      const testCases = [
        { stored: 'invalid', expected: 120 },
        { stored: '-100', expected: 120 },
        { stored: '0', expected: 120 },
        { stored: 'null', expected: 120 },
        { stored: '', expected: 120 },
      ]

      testCases.forEach(({ stored, expected }) => {
        localStorage.setItem('restDuration', stored)
        const { result } = renderHook(() => useWorkoutStore())

        expect(result.current.getRestDuration()).toBe(expected)
      })
    })

    it('should clamp duration to valid range', () => {
      const testCases = [
        { stored: '3', expected: 5 }, // Below minimum
        { stored: '700', expected: 600 }, // Above maximum
        { stored: '150', expected: 150 }, // Within range
      ]

      testCases.forEach(({ stored, expected }) => {
        localStorage.setItem('restDuration', stored)
        const { result } = renderHook(() => useWorkoutStore())

        expect(result.current.getRestDuration()).toBe(expected)
      })
    })

    it('should update when localStorage changes', () => {
      const { result } = renderHook(() => useWorkoutStore())

      expect(result.current.getRestDuration()).toBe(120)

      // Simulate localStorage change
      localStorage.setItem('restDuration', '90')
      window.dispatchEvent(new Event('storage'))

      // Store should react to storage event and update
      expect(result.current.getRestDuration()).toBe(90)
    })
  })
})

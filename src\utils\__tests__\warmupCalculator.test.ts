import { describe, it, expect, vi } from 'vitest'
import { WarmupCalculator, WEIGHTED_EXERCISE_IDS } from '../warmupCalculator'
import type { RecommendationModel, ExerciseModel } from '@/types'
import type { WarmupCalculationConfig } from '../warmupCalculator'

// Helper function to convert old API to new API for backward compatibility in tests
function computeWarmups(
  recommendation: RecommendationModel,
  exercise: ExerciseModel
) {
  const config: WarmupCalculationConfig = {
    warmupsCount: recommendation.WarmupsCount || 0,
    workingWeight: recommendation.Weight || { Kg: 0, Lb: 0 },
    workingReps: recommendation.Reps || 0,
    incrementValue: 2.5,
    isPlateAvailable: recommendation.isPlateAvailable || false,
    isDumbbellAvailable: false,
    isBandsAvailable: false,
    isPulleyAvailable: false,
    isBodyweight: exercise.IsBodyweight || false,
    isWeighted: WEIGHTED_EXERCISE_IDS.includes(exercise.Id),
    isAssisted: false,
    barbellWeight: 20,
    availablePlates: 'all',
    availableDumbbells: 'all',
    availableBands: 'all',
    userBodyWeight: 80,
    isKg: true,
  }

  return WarmupCalculator.computeWarmups(config)
}

// Mock getUserSettings
vi.mock('@/stores/authStore', () => ({
  useAuthStore: {
    getState: () => ({
      userSettings: {
        barbellWeight: 45,
        unit: 'lbs',
      },
    }),
  },
}))

describe('computeWarmups', () => {
  const createMockRecommendation = (
    overrides?: Partial<RecommendationModel>
  ): RecommendationModel => ({
    Series: 3,
    Reps: 10,
    Weight: { Lb: 100, Kg: 45.36 },
    WarmupsCount: 2,
    Increments: { Lb: 5, Kg: 2.5 },
    isPlateAvailable: false,
    isDumbbellAvailable: true,
    isPulleyAvailable: false,
    isBandsAvailable: false,
    OneRMProgress: 0,
    RecommendationInKg: 45.36,
    OneRMPercentage: 75,
    WarmUpReps1: 0,
    WarmUpReps2: 0,
    WarmUpWeightSet1: { Lb: 0, Kg: 0 },
    WarmUpWeightSet2: { Lb: 0, Kg: 0 },
    WarmUpsList: [],
    RpRest: 180,
    NbPauses: 0,
    NbRepsPauses: 0,
    IsEasy: false,
    IsMedium: true,
    IsBodyweight: false,
    Min: { Lb: 0, Kg: 0 },
    Max: { Lb: 200, Kg: 90.72 },
    IsNormalSets: true,
    IsDeload: false,
    IsBackOffSet: false,
    BackOffSetWeight: { Lb: 0, Kg: 0 },
    IsMaxChallenge: false,
    IsLightSession: false,
    FirstWorkSetReps: 10,
    FirstWorkSetWeight: { Lb: 100, Kg: 45.36 },
    FirstWorkSet1RM: { Lb: 133, Kg: 60.33 },
    IsPyramid: false,
    IsReversePyramid: false,
    HistorySet: [],
    ReferenceSetHistory: {
      Id: 1,
      Reps: 10,
      Weight: { Lb: 95, Kg: 43.09 },
      IsWarmups: false,
      IsNext: false,
      IsFinished: true,
    },
    MinReps: 8,
    MaxReps: 12,
    Speed: 1,
    IsManual: false,
    ReferenseReps: 10,
    ReferenseWeight: { Lb: 95, Kg: 43.09 },
    IsDropSet: false,
    ...overrides,
  })

  const createMockExercise = (
    overrides?: Partial<ExerciseModel>
  ): ExerciseModel => ({
    Id: 1,
    Label: 'Bench Press',
    IsSystemExercise: true,
    IsSwapTarget: false,
    IsFinished: false,
    IsUnilateral: false,
    IsTimeBased: false,
    IsEasy: false,
    IsMedium: true,
    IsBodyweight: false,
    VideoUrl: '',
    IsNextExercise: false,
    IsPlate: false,
    IsWeighted: true,
    IsPyramid: false,
    IsNormalSets: true,
    IsBodypartPriority: false,
    IsFlexibility: false,
    IsOneHanded: false,
    LocalVideo: '',
    IsAssisted: false,
    ...overrides,
  })

  describe('when WarmupsCount is 0', () => {
    it('should return empty array', () => {
      const recommendation = createMockRecommendation({ WarmupsCount: 0 })
      const exercise = createMockExercise()

      const result = computeWarmups(recommendation, exercise)

      expect(result).toEqual([])
    })
  })

  describe('for weighted exercises', () => {
    it('should calculate 2 warmup sets correctly', () => {
      const recommendation = createMockRecommendation({
        WarmupsCount: 2,
        Weight: { Lb: 100, Kg: 45.36 },
        Reps: 10,
      })
      const exercise = createMockExercise({ IsBodyweight: false })

      const result = computeWarmups(recommendation, exercise)

      expect(result).toHaveLength(2)

      // First warmup: ~50% of working weight
      expect(result[0].WarmUpWeightSet.Kg).toBeCloseTo(22.5, 1) // 50% of 45.36 kg
      expect(result[0].WarmUpReps).toBeGreaterThan(0)

      // Second warmup: ~85% of working weight
      expect(result[1].WarmUpWeightSet.Kg).toBeCloseTo(37.5, 1) // 85% of 45.36 kg
      expect(result[1].WarmUpReps).toBeLessThan(result[0].WarmUpReps)
    })

    it('should calculate 1 warmup set correctly', () => {
      const recommendation = createMockRecommendation({
        WarmupsCount: 1,
        Weight: { Lb: 100, Kg: 45.36 },
        Reps: 10,
      })
      const exercise = createMockExercise({ IsBodyweight: false })

      const result = computeWarmups(recommendation, exercise)

      expect(result).toHaveLength(1)

      // Single warmup: should be around 50% of working weight
      expect(result[0].WarmUpWeightSet.Kg).toBeCloseTo(22.5, 1) // 50% of 45.36 kg
      expect(result[0].WarmUpReps).toBeGreaterThan(0)
    })

    it('should calculate 3 warmup sets with proper progression', () => {
      const recommendation = createMockRecommendation({
        WarmupsCount: 3,
        Weight: { Lb: 200, Kg: 90.72 },
        Reps: 8,
      })
      const exercise = createMockExercise({ IsBodyweight: false })

      const result = computeWarmups(recommendation, exercise)

      expect(result).toHaveLength(3)

      // First warmup: ~50% of working weight
      expect(result[0].WarmUpWeightSet.Kg).toBeCloseTo(45.0, 1) // 50% of 90.72 kg

      // Progressive weight increase
      expect(result[1].WarmUpWeightSet.Kg).toBeGreaterThan(
        result[0].WarmUpWeightSet.Kg
      )
      expect(result[2].WarmUpWeightSet.Kg).toBeGreaterThan(
        result[1].WarmUpWeightSet.Kg
      )

      // Progressive reps decrease
      expect(result[0].WarmUpReps).toBeGreaterThan(result[1].WarmUpReps)
      expect(result[1].WarmUpReps).toBeGreaterThan(result[2].WarmUpReps)
    })

    it('should ensure minimum 3 reps when plates not available', () => {
      const recommendation = createMockRecommendation({
        WarmupsCount: 2,
        Weight: { Lb: 300, Kg: 136.08 },
        Reps: 5,
        isPlateAvailable: false,
      })
      const exercise = createMockExercise({ IsBodyweight: false })

      const result = computeWarmups(recommendation, exercise)

      result.forEach((warmup) => {
        // All warmups should have minimum 3 reps
        expect(warmup.WarmUpReps).toBeGreaterThanOrEqual(3)
      })
    })
  })

  describe('for bodyweight exercises', () => {
    it('should calculate warmup sets with same weight but reduced reps', () => {
      const recommendation = createMockRecommendation({
        WarmupsCount: 2,
        Weight: { Lb: 0, Kg: 0 },
        Reps: 15,
        IsBodyweight: true,
      })
      const exercise = createMockExercise({ IsBodyweight: true })

      const result = computeWarmups(recommendation, exercise)

      expect(result).toHaveLength(2)

      // All warmups should have same weight (bodyweight)
      result.forEach((warmup) => {
        expect(warmup.WarmUpWeightSet.Lb).toBe(0)
        expect(warmup.WarmUpWeightSet.Kg).toBe(0)
      })

      // First warmup: ~40% of target reps
      expect(result[0].WarmUpReps).toBeCloseTo(6, 0)

      // Second warmup: ~60% of target reps
      expect(result[1].WarmUpReps).toBeCloseTo(9, 0)
    })

    it('should handle single warmup set for bodyweight', () => {
      const recommendation = createMockRecommendation({
        WarmupsCount: 1,
        Weight: { Lb: 0, Kg: 0 },
        Reps: 20,
        IsBodyweight: true,
      })
      const exercise = createMockExercise({ IsBodyweight: true })

      const result = computeWarmups(recommendation, exercise)

      expect(result).toHaveLength(1)
      expect(result[0].WarmUpReps).toBe(8) // 40% of 20
    })
  })

  describe('with plate calculations', () => {
    it('should apply plate weight calculations when plates available', () => {
      const recommendation = createMockRecommendation({
        WarmupsCount: 2,
        Weight: { Lb: 135, Kg: 61.23 },
        Reps: 10,
        isPlateAvailable: true,
      })
      const exercise = createMockExercise({
        IsBodyweight: false,
        IsPlate: true,
      })

      const result = computeWarmups(recommendation, exercise)

      // Weights should be rounded to plate increments
      // Second warmup should be ~85% of working weight, rounded to available plates
      expect(result[1].WarmUpWeightSet.Kg).toBeCloseTo(52.5, 1) // ~85% of 61.23 kg
      expect(result[1].WarmUpWeightSet.Kg).toBeGreaterThan(
        result[0].WarmUpWeightSet.Kg
      )
    })
  })

  describe('edge cases', () => {
    it('should handle exercises with very low reps', () => {
      const recommendation = createMockRecommendation({
        WarmupsCount: 2,
        Weight: { Lb: 300, Kg: 136.08 },
        Reps: 3,
      })
      const exercise = createMockExercise({ IsBodyweight: false })

      const result = computeWarmups(recommendation, exercise)

      // Should still generate valid warmups
      expect(result).toHaveLength(2)
      result.forEach((warmup) => {
        expect(warmup.WarmUpReps).toBeGreaterThan(0)
      })
    })

    it('should handle missing weight data', () => {
      const recommendation = createMockRecommendation({
        WarmupsCount: 2,
        Weight: { Lb: 0, Kg: 0 },
        Reps: 10,
        IsBodyweight: false,
      })
      const exercise = createMockExercise({ IsBodyweight: false })

      const result = computeWarmups(recommendation, exercise)

      // Should return empty array or handle gracefully
      expect(result).toEqual([])
    })

    it('should avoid floating-point precision issues', () => {
      // Test case that reproduces the floating-point precision issue
      const recommendation = createMockRecommendation({
        WarmupsCount: 1,
        Weight: { Lb: 40, Kg: 18.14 }, // Similar to respalz exercise
        Reps: 16,
      })
      const exercise = createMockExercise({ IsBodyweight: false })

      const result = computeWarmups(recommendation, exercise)

      expect(result).toHaveLength(1)

      // Weight should be clean (no floating-point artifacts)
      const weightLb = result[0].WarmUpWeightSet.Lb
      const weightKg = result[0].WarmUpWeightSet.Kg

      // Values should be properly calculated (50% of working weight)
      expect(weightKg).toBeCloseTo(10, 1) // 50% of 18.14 kg (rounded)
      expect(weightLb).toBeCloseTo(22, 1) // Converted to lbs

      // Verify no floating-point precision artifacts
      expect(Number.isFinite(weightLb)).toBe(true)
      expect(Number.isFinite(weightKg)).toBe(true)

      // Check that values are properly rounded (allow up to 4 decimal places for precision)
      expect(weightLb.toString()).not.toMatch(/\.\d{5,}/) // No more than 4 decimal places
      expect(weightKg.toString()).not.toMatch(/\.\d{5,}/) // No more than 4 decimal places
    })

    it('should calculate correct warmup for Rest-Pause exercise scenario', () => {
      // Test case that reproduces the Rest-Pause warmup issue
      const recommendation = createMockRecommendation({
        WarmupsCount: 1,
        Weight: { Lb: 88, Kg: 40 }, // Working weight: 40 kg
        Reps: 16, // Main working set reps
        IsNormalSets: false, // Rest-Pause exercise
        NbPauses: 3,
        NbRepsPauses: 8, // Rest-pause mini-set reps (different from main set)
      })
      const exercise = createMockExercise({ IsBodyweight: false })

      const result = computeWarmups(recommendation, exercise)

      expect(result).toHaveLength(1)

      // Warmup should be calculated based on main working set (16 reps, 40 kg)
      // NOT the same as working set
      const warmupReps = result[0].WarmUpReps
      const warmupWeightKg = result[0].WarmUpWeightSet.Kg

      // Warmup should be ~75% of working reps = 12 reps (not 16)
      expect(warmupReps).toBeCloseTo(12, 0)

      // Warmup should be ~50% of working weight = 20 kg (not 40)
      expect(warmupWeightKg).toBeCloseTo(20, 0)

      // Verify it's NOT the same as working set
      expect(warmupReps).not.toBe(recommendation.Reps)
      expect(warmupWeightKg).not.toBe(recommendation.Weight.Kg)
    })
  })
})

describe('WarmupCalculator (New Implementation)', () => {
  describe('Weighted Exercise Warmups', () => {
    it('should calculate 3 warmup sets for bench press', () => {
      const config: WarmupCalculationConfig = {
        warmupsCount: 3,
        workingWeight: { Kg: 100, Lb: 220 },
        workingReps: 10,
        incrementValue: 2.5,
        isPlateAvailable: true,
        isBodyweight: false,
        barbellWeight: 20,
        availablePlates: 'all',
        userBodyWeight: 80,
        isKg: true,
      }

      const warmups = WarmupCalculator.computeWarmups(config)

      expect(warmups).toHaveLength(3)

      // First warmup should be ~50% of working weight (API reference guide)
      expect(warmups[0].WarmUpWeightSet.Kg).toBeCloseTo(50, 0)

      // Second warmup should be between 50% and 85% (actual progression)
      expect(warmups[1].WarmUpWeightSet.Kg).toBeGreaterThan(50)
      expect(warmups[1].WarmUpWeightSet.Kg).toBeLessThan(85)

      // Third warmup ~85% of working weight
      expect(warmups[2].WarmUpWeightSet.Kg).toBeCloseTo(85, 0)

      // Reps should decrease
      expect(warmups[0].WarmUpReps).toBeGreaterThan(warmups[2].WarmUpReps)
    })

    it('should apply minimum reps for weighted exercises', () => {
      const config: WarmupCalculationConfig = {
        warmupsCount: 2,
        workingWeight: { Kg: 200, Lb: 440 },
        workingReps: 3, // Low reps
        incrementValue: 2.5,
        isPlateAvailable: false,
        isBodyweight: false,
        barbellWeight: 20,
        availablePlates: '',
        userBodyWeight: 80,
        isKg: true,
      }

      const warmups = WarmupCalculator.computeWarmups(config)

      // Should enforce minimum 3 reps for weighted exercises
      warmups.forEach((warmup) => {
        // All warmups should have minimum 3 reps
        expect(warmup.WarmUpReps).toBeGreaterThanOrEqual(3)
      })
    })
  })

  describe('Bodyweight Exercise Warmups', () => {
    it('should calculate warmups for push-ups', () => {
      const config: WarmupCalculationConfig = {
        warmupsCount: 2,
        workingWeight: { Kg: 80, Lb: 176 }, // User bodyweight
        workingReps: 20,
        incrementValue: 1,
        isPlateAvailable: false,
        isBodyweight: true,
        barbellWeight: 0,
        availablePlates: '',
        userBodyWeight: 80,
        isKg: true,
      }

      const warmups = WarmupCalculator.computeWarmups(config)

      expect(warmups).toHaveLength(2)

      // Weight should be 0 for bodyweight exercises (new implementation)
      expect(warmups[0].WarmUpWeightSet.Kg).toBe(0)
      expect(warmups[1].WarmUpWeightSet.Kg).toBe(0)

      // Reps should start at 40% and progress to 60%
      expect(warmups[0].WarmUpReps).toBeCloseTo(8, 0) // 40% of 20
      expect(warmups[1].WarmUpReps).toBeCloseTo(12, 0) // 60% of 20
    })
  })

  describe('Edge Cases', () => {
    it('should return empty array for zero warmups', () => {
      const config: WarmupCalculationConfig = {
        warmupsCount: 0,
        workingWeight: { Kg: 100, Lb: 220 },
        workingReps: 10,
        incrementValue: 2.5,
        isPlateAvailable: false,
        isBodyweight: false,
        barbellWeight: 20,
        availablePlates: '',
        userBodyWeight: 80,
        isKg: true,
      }

      const warmups = WarmupCalculator.computeWarmups(config)

      expect(warmups).toEqual([])
    })

    it('should handle missing weight data for non-bodyweight exercises', () => {
      const config: WarmupCalculationConfig = {
        warmupsCount: 2,
        workingWeight: { Kg: 0, Lb: 0 },
        workingReps: 10,
        incrementValue: 2.5,
        isPlateAvailable: false,
        isBodyweight: false,
        barbellWeight: 20,
        availablePlates: '',
        userBodyWeight: 80,
        isKg: true,
      }

      const warmups = WarmupCalculator.computeWarmups(config)

      expect(warmups).toEqual([])
    })

    it('should respect min and max weight constraints', () => {
      const config: WarmupCalculationConfig = {
        warmupsCount: 3,
        workingWeight: { Kg: 100, Lb: 220 },
        workingReps: 10,
        incrementValue: 2.5,
        minWeight: 30,
        maxWeight: 80,
        isPlateAvailable: false,
        isBodyweight: false,
        barbellWeight: 20,
        availablePlates: '',
        userBodyWeight: 80,
        isKg: true,
      }

      const warmups = WarmupCalculator.computeWarmups(config)

      warmups.forEach((warmup) => {
        if (warmup.WarmUpWeightSet.Kg > 0) {
          expect(warmup.WarmUpWeightSet.Kg).toBeGreaterThanOrEqual(30)
          expect(warmup.WarmUpWeightSet.Kg).toBeLessThanOrEqual(80)
        }
      })
    })
  })

  describe('Unit Conversions', () => {
    it('should properly convert between kg and lb', () => {
      const config: WarmupCalculationConfig = {
        warmupsCount: 1,
        workingWeight: { Kg: 100, Lb: 220.46 },
        workingReps: 10,
        incrementValue: 2.5,
        isPlateAvailable: false,
        isBodyweight: false,
        barbellWeight: 20,
        availablePlates: '',
        userBodyWeight: 80,
        isKg: true,
      }

      const warmups = WarmupCalculator.computeWarmups(config)

      expect(warmups).toHaveLength(1)
      // Check that kg to lb conversion is correct (approximately 2.20462)
      if (warmups[0].WarmUpWeightSet.Kg > 0) {
        const expectedLb = warmups[0].WarmUpWeightSet.Kg * 2.20462
        expect(warmups[0].WarmUpWeightSet.Lb).toBeCloseTo(expectedLb, 1)
      }
    })
  })

  describe('Enhanced Features (New Implementation)', () => {
    describe('Weighted Bodyweight Exercises', () => {
      it('should handle weighted pull-ups correctly', () => {
        const config: WarmupCalculationConfig = {
          warmupsCount: 3,
          workingWeight: { Kg: 20, Lb: 44 }, // 20kg additional weight
          workingReps: 8,
          incrementValue: 2.5,
          isPlateAvailable: false,
          isBodyweight: false,
          isWeighted: true, // This is a weighted bodyweight exercise
          barbellWeight: 0,
          availablePlates: '',
          userBodyWeight: 80,
          isKg: true,
        }

        const warmups = WarmupCalculator.computeWarmups(config)

        expect(warmups).toHaveLength(3)

        // First warmup should be bodyweight only (0kg additional weight)
        expect(warmups[0].WarmUpWeightSet.Kg).toBe(0)
        expect(warmups[0].WarmUpWeightSet.Lb).toBe(0)

        // Subsequent warmups should progress toward working weight
        expect(warmups[1].WarmUpWeightSet.Kg).toBeGreaterThan(0)
        expect(warmups[2].WarmUpWeightSet.Kg).toBeGreaterThan(
          warmups[1].WarmUpWeightSet.Kg
        )

        // All should have reasonable rep counts
        warmups.forEach((warmup) => {
          expect(warmup.WarmUpReps).toBeGreaterThan(0)
          expect(warmup.WarmUpReps).toBeLessThanOrEqual(config.workingReps)
        })
      })

      it('should handle weighted dips correctly', () => {
        const config: WarmupCalculationConfig = {
          warmupsCount: 2,
          workingWeight: { Kg: 15, Lb: 33 }, // 15kg additional weight
          workingReps: 12,
          incrementValue: 2.5,
          isPlateAvailable: false,
          isBodyweight: false,
          isWeighted: true,
          barbellWeight: 0,
          availablePlates: '',
          userBodyWeight: 75,
          isKg: true,
        }

        const warmups = WarmupCalculator.computeWarmups(config)

        expect(warmups).toHaveLength(2)

        // First warmup: bodyweight only
        expect(warmups[0].WarmUpWeightSet.Kg).toBe(0)

        // Second warmup: progression toward working weight
        expect(warmups[1].WarmUpWeightSet.Kg).toBeGreaterThan(0)
        expect(warmups[1].WarmUpWeightSet.Kg).toBeLessThan(
          config.workingWeight.Kg
        )
      })
    })

    describe('Equipment-Specific Rounding', () => {
      it('should apply plate rounding for barbell exercises', () => {
        const config: WarmupCalculationConfig = {
          warmupsCount: 2,
          workingWeight: { Kg: 100, Lb: 220 },
          workingReps: 5,
          incrementValue: 2.5,
          isPlateAvailable: true,
          isBodyweight: false,
          barbellWeight: 20, // 20kg barbell
          availablePlates: 'all',
          userBodyWeight: 80,
          isKg: true,
        }

        const warmups = WarmupCalculator.computeWarmups(config)

        expect(warmups).toHaveLength(2)

        // Weights should be rounded to plate-loadable increments
        warmups.forEach((warmup) => {
          const plateWeight = warmup.WarmUpWeightSet.Kg - config.barbellWeight
          // Should be divisible by plate increments (considering both sides)
          expect(plateWeight % 5).toBeCloseTo(0, 1) // 2.5kg plates on each side = 5kg total
        })
      })

      it('should handle dumbbell weight selection', () => {
        const config: WarmupCalculationConfig = {
          warmupsCount: 2,
          workingWeight: { Kg: 30, Lb: 66 },
          workingReps: 10,
          incrementValue: 2.5,
          isPlateAvailable: false,
          isDumbbellAvailable: true,
          isBodyweight: false,
          availableDumbbells:
            '10_2_true|15_2_true|20_2_true|25_2_true|30_2_true',
          barbellWeight: 0,
          availablePlates: '',
          userBodyWeight: 80,
          isKg: true,
        }

        const warmups = WarmupCalculator.computeWarmups(config)

        expect(warmups).toHaveLength(2)

        // Weights should match available dumbbell weights
        const availableWeights = [10, 15, 20, 25, 30]
        warmups.forEach((warmup) => {
          expect(availableWeights).toContain(warmup.WarmUpWeightSet.Kg)
        })
      })
    })

    describe('Rest-Pause Exercise Integration', () => {
      it('should calculate warmups correctly for Rest-Pause exercises', () => {
        const config: WarmupCalculationConfig = {
          warmupsCount: 1,
          workingWeight: { Kg: 40, Lb: 88 },
          workingReps: 16, // Main set reps (not pause reps)
          incrementValue: 2.5,
          isPlateAvailable: false,
          isBodyweight: false,
          barbellWeight: 20,
          availablePlates: '',
          userBodyWeight: 80,
          isKg: true,
        }

        const warmups = WarmupCalculator.computeWarmups(config)

        expect(warmups).toHaveLength(1)

        // Should be based on main working set, not pause mini-sets
        expect(warmups[0].WarmUpWeightSet.Kg).toBeCloseTo(20, 1) // ~50% of 40kg
        expect(warmups[0].WarmUpReps).toBeGreaterThan(8) // Should be reasonable for warmup
        expect(warmups[0].WarmUpReps).toBeLessThan(16) // Less than working reps

        // Should NOT be the same as working set (the original bug)
        expect(warmups[0].WarmUpWeightSet.Kg).not.toBe(config.workingWeight.Kg)
        expect(warmups[0].WarmUpReps).not.toBe(config.workingReps)
      })
    })
  })
})
